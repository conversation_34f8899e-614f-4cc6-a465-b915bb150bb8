<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Daily School Attendance System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <style>
      /* Custom Crystal Screen Styles */
      .crystal-screen {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      }

      .desktop-bg {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        position: relative;
      }

      .desktop-bg::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: radial-gradient(
            circle at 25% 25%,
            rgba(255, 255, 255, 0.1) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 75% 75%,
            rgba(255, 255, 255, 0.1) 0%,
            transparent 50%
          );
        pointer-events: none;
      }

      .btn-crystal {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
      }

      .btn-crystal:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
      }

      .status-present {
        background: rgba(34, 197, 94, 0.2);
        color: #16a34a;
      }
      .status-absent {
        background: rgba(239, 68, 68, 0.2);
        color: #dc2626;
      }
      .status-late {
        background: rgba(245, 158, 11, 0.2);
        color: #d97706;
      }
      .status-excused {
        background: rgba(59, 130, 246, 0.2);
        color: #2563eb;
      }
      .status-pending {
        background: rgba(156, 163, 175, 0.2);
        color: #6b7280;
      }

      .fade-in {
        animation: fadeIn 0.5s ease-in;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .hidden {
        display: none !important;
      }

      .taskbar {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 60px;
        background: rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(20px);
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 20px;
        z-index: 1000;
      }

      .time-display {
        color: white;
        font-weight: 500;
      }

      .user-info {
        color: white;
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .check-in-panel {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 15px;
        padding: 15px;
        margin-right: 20px;
        display: flex;
        align-items: center;
        gap: 15px;
      }

      .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        animation: pulse 2s infinite;
      }

      .status-in { background: #10b981; }
      .status-out { background: #ef4444; }

      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
      }

      .check-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 8px 16px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 14px;
      }

      .check-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-1px);
      }

      .check-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    </style>
  </head>
  <body class="desktop-bg">
    <!-- Login Screen -->
    <div
      id="loginScreen"
      class="flex items-center justify-center min-h-screen p-4"
    >
      <div class="crystal-screen p-8 w-full max-w-md fade-in">
        <div class="text-center mb-8">
          <i class="fas fa-graduation-cap text-4xl text-white mb-4"></i>
          <h1 class="text-2xl font-bold text-white mb-2">
            School Attendance System
          </h1>
          <p class="text-gray-200">Please select your role and login</p>
        </div>

        <div class="space-y-4">
          <div>
            <label class="block text-white text-sm font-medium mb-2"
              >Role</label
            >
            <select
              id="roleSelect"
              class="w-full p-3 rounded-lg bg-white/20 text-white border border-white/30 focus:outline-none focus:ring-2 focus:ring-white/50"
            >
              <option value="admin">Administrator</option>
              <option value="parent">Parent</option>
              <option value="student">Student</option>
            </select>
          </div>

          <div>
            <label class="block text-white text-sm font-medium mb-2"
              >Username</label
            >
            <input
              type="text"
              id="username"
              class="w-full p-3 rounded-lg bg-white/20 text-white placeholder-gray-300 border border-white/30 focus:outline-none focus:ring-2 focus:ring-white/50"
              placeholder="Enter username"
            />
          </div>

          <div>
            <label class="block text-white text-sm font-medium mb-2"
              >Password</label
            >
            <input
              type="password"
              id="password"
              class="w-full p-3 rounded-lg bg-white/20 text-white placeholder-gray-300 border border-white/30 focus:outline-none focus:ring-2 focus:ring-white/50"
              placeholder="Enter password"
            />
          </div>

          <button
            onclick="login()"
            class="w-full btn-crystal text-white font-medium py-3 px-4 rounded-lg"
          >
            <i class="fas fa-sign-in-alt mr-2"></i>Login
          </button>
        </div>

        <div class="mt-6 text-sm text-gray-300">
          <p class="font-medium mb-2">Demo Credentials:</p>
          <p><strong>Admin:</strong> admin / admin123</p>
          <p><strong>Parent:</strong> parent1 / parent123</p>
          <p><strong>Student:</strong> student1 / student123</p>
        </div>
      </div>
    </div>

    <!-- Admin Dashboard -->
    <div id="adminDashboard" class="hidden p-6 pb-20">
      <div class="crystal-screen p-6 fade-in">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-2xl font-bold text-white flex items-center">
            <i class="fas fa-user-shield mr-3"></i>Administrator Dashboard
          </h2>
          <button
            onclick="logout()"
            class="btn-crystal text-white px-4 py-2 rounded-lg"
          >
            <i class="fas fa-sign-out-alt mr-2"></i>Logout
          </button>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          <div class="crystal-screen p-4">
            <h3 class="text-white font-medium mb-2">Total Students</h3>
            <p class="text-3xl font-bold text-white" id="totalStudents">0</p>
          </div>
          <div class="crystal-screen p-4">
            <h3 class="text-white font-medium mb-2">Present Today</h3>
            <p class="text-3xl font-bold text-green-400" id="presentToday">0</p>
          </div>
          <div class="crystal-screen p-4">
            <h3 class="text-white font-medium mb-2">Absent Today</h3>
            <p class="text-3xl font-bold text-red-400" id="absentToday">0</p>
          </div>
        </div>

        <div class="crystal-screen p-6">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-bold text-white">Attendance Management</h3>
            <div class="flex gap-4">
              <input
                type="date"
                id="attendanceDate"
                class="p-2 rounded-lg bg-white/20 text-white border border-white/30 focus:outline-none focus:ring-2 focus:ring-white/50"
              />
              <button
                onclick="saveAttendance()"
                class="btn-crystal text-white px-4 py-2 rounded-lg"
              >
                <i class="fas fa-save mr-2"></i>Save Changes
              </button>
            </div>
          </div>

          <div class="overflow-x-auto">
            <table class="w-full text-white">
              <thead>
                <tr class="border-b border-white/20">
                  <th class="text-left p-3">Student ID</th>
                  <th class="text-left p-3">Name</th>
                  <th class="text-left p-3">Class</th>
                  <th class="text-left p-3">Status</th>
                  <th class="text-left p-3">Actions</th>
                </tr>
              </thead>
              <tbody id="studentAttendanceTable">
                <!-- Dynamic content -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Parent Dashboard -->
    <div id="parentDashboard" class="hidden p-6 pb-20">
      <div class="crystal-screen p-6 fade-in">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-2xl font-bold text-white flex items-center">
            <i class="fas fa-user-friends mr-3"></i>Parent Dashboard
          </h2>
          <button
            onclick="logout()"
            class="btn-crystal text-white px-4 py-2 rounded-lg"
          >
            <i class="fas fa-sign-out-alt mr-2"></i>Logout
          </button>
        </div>

        <div class="crystal-screen p-6">
          <h3 class="text-xl font-bold text-white mb-4">
            Child's Attendance Record
          </h3>
          <div id="parentAttendanceView">
            <!-- Dynamic content -->
          </div>
        </div>
      </div>
    </div>

    <!-- Student Dashboard -->
    <div id="studentDashboard" class="hidden p-6 pb-20">
      <div class="crystal-screen p-6 fade-in">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-2xl font-bold text-white flex items-center">
            <i class="fas fa-user-graduate mr-3"></i>Student Dashboard
          </h2>
          <button
            onclick="logout()"
            class="btn-crystal text-white px-4 py-2 rounded-lg"
          >
            <i class="fas fa-sign-out-alt mr-2"></i>Logout
          </button>
        </div>

        <div class="crystal-screen p-6">
          <h3 class="text-xl font-bold text-white mb-4">
            My Attendance Record
          </h3>
          <div id="studentAttendanceView">
            <!-- Dynamic content -->
          </div>
        </div>
      </div>
    </div>

    <!-- Custom Message Box -->
    <div
      id="messageBox"
      class="hidden fixed inset-0 bg-black/50 flex items-center justify-center z-50"
    >
      <div class="crystal-screen p-6 max-w-md w-full mx-4">
        <h3 class="text-white font-bold mb-4" id="messageTitle">Message</h3>
        <p class="text-gray-200 mb-6" id="messageText"></p>
        <button
          onclick="closeMessage()"
          class="btn-crystal text-white px-4 py-2 rounded-lg w-full"
        >
          OK
        </button>
      </div>
    </div>

    <!-- Taskbar -->
    <div class="taskbar">
      <div class="user-info">
        <i class="fas fa-user-circle"></i>
        <span id="currentUser">Not logged in</span>
      </div>
      <div class="time-display" id="currentTime"></div>
    </div>

    <script>
      // Mock Data
      const mockUsers = {
        admin: {
          username: "admin",
          password: "admin123",
          role: "admin",
          name: "Administrator",
        },
        parent1: {
          username: "parent1",
          password: "parent123",
          role: "parent",
          name: "John Smith",
          children: ["STU001"],
        },
        parent2: {
          username: "parent2",
          password: "parent123",
          role: "parent",
          name: "Mary Johnson",
          children: ["STU002", "STU003"],
        },
        student1: {
          username: "student1",
          password: "student123",
          role: "student",
          name: "Alice Smith",
          studentId: "STU001",
        },
        student2: {
          username: "student2",
          password: "student123",
          role: "student",
          name: "Bob Johnson",
          studentId: "STU002",
        },
        student3: {
          username: "student3",
          password: "student123",
          role: "student",
          name: "Charlie Johnson",
          studentId: "STU003",
        },
      };

      const mockStudents = {
        STU001: {
          id: "STU001",
          name: "Alice Smith",
          class: "Grade 10A",
          parentId: "parent1",
        },
        STU002: {
          id: "STU002",
          name: "Bob Johnson",
          class: "Grade 9B",
          parentId: "parent2",
        },
        STU003: {
          id: "STU003",
          name: "Charlie Johnson",
          class: "Grade 11A",
          parentId: "parent2",
        },
        STU004: {
          id: "STU004",
          name: "Diana Wilson",
          class: "Grade 10B",
          parentId: null,
        },
        STU005: {
          id: "STU005",
          name: "Edward Brown",
          class: "Grade 9A",
          parentId: null,
        },
      };

      let mockAttendance = {};
      let currentUser = null;

      // Initialize attendance data
      function initializeAttendance() {
        const today = new Date().toISOString().split("T")[0];
        const yesterday = new Date(Date.now() - 86400000)
          .toISOString()
          .split("T")[0];
        const dayBefore = new Date(Date.now() - 172800000)
          .toISOString()
          .split("T")[0];

        mockAttendance = {
          [today]: {
            STU001: "present",
            STU002: "absent",
            STU003: "late",
            STU004: "present",
            STU005: "excused",
          },
          [yesterday]: {
            STU001: "present",
            STU002: "present",
            STU003: "present",
            STU004: "absent",
            STU005: "present",
          },
          [dayBefore]: {
            STU001: "late",
            STU002: "present",
            STU003: "absent",
            STU004: "present",
            STU005: "present",
          },
        };
      }

      // Utility Functions
      function showMessage(title, text) {
        document.getElementById("messageTitle").textContent = title;
        document.getElementById("messageText").textContent = text;
        document.getElementById("messageBox").classList.remove("hidden");
      }

      function closeMessage() {
        document.getElementById("messageBox").classList.add("hidden");
      }

      function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString();
        const dateString = now.toLocaleDateString();
        document.getElementById(
          "currentTime"
        ).textContent = `${dateString} ${timeString}`;
      }

      function hideAllDashboards() {
        document.getElementById("loginScreen").classList.add("hidden");
        document.getElementById("adminDashboard").classList.add("hidden");
        document.getElementById("parentDashboard").classList.add("hidden");
        document.getElementById("studentDashboard").classList.add("hidden");
      }

      // Authentication
      function login() {
        const role = document.getElementById("roleSelect").value;
        const username = document.getElementById("username").value;
        const password = document.getElementById("password").value;

        if (!username || !password) {
          showMessage("Error", "Please enter both username and password.");
          return;
        }

        const user = Object.values(mockUsers).find(
          (u) =>
            u.username === username &&
            u.password === password &&
            u.role === role
        );

        if (user) {
          currentUser = user;
          document.getElementById("currentUser").textContent = user.name;
          hideAllDashboards();

          switch (role) {
            case "admin":
              showAdminDashboard();
              break;
            case "parent":
              showParentDashboard();
              break;
            case "student":
              showStudentDashboard();
              break;
          }

          showMessage("Success", `Welcome, ${user.name}!`);
        } else {
          showMessage(
            "Error",
            "Invalid credentials. Please check your username, password, and role."
          );
        }
      }

      function logout() {
        currentUser = null;
        document.getElementById("currentUser").textContent = "Not logged in";
        document.getElementById("username").value = "";
        document.getElementById("password").value = "";
        hideAllDashboards();
        document.getElementById("loginScreen").classList.remove("hidden");
        showMessage("Success", "You have been logged out successfully.");
      }

      // Admin Dashboard Functions
      function showAdminDashboard() {
        document.getElementById("adminDashboard").classList.remove("hidden");

        // Set today's date
        const today = new Date().toISOString().split("T")[0];
        document.getElementById("attendanceDate").value = today;

        updateAdminStats();
        loadStudentAttendanceTable();
      }

      function updateAdminStats() {
        const totalStudents = Object.keys(mockStudents).length;
        const today = document.getElementById("attendanceDate").value;
        const todayAttendance = mockAttendance[today] || {};

        const presentCount = Object.values(todayAttendance).filter(
          (status) => status === "present"
        ).length;
        const absentCount = Object.values(todayAttendance).filter(
          (status) => status === "absent"
        ).length;

        document.getElementById("totalStudents").textContent = totalStudents;
        document.getElementById("presentToday").textContent = presentCount;
        document.getElementById("absentToday").textContent = absentCount;
      }

      function loadStudentAttendanceTable() {
        const selectedDate = document.getElementById("attendanceDate").value;
        const tbody = document.getElementById("studentAttendanceTable");
        tbody.innerHTML = "";

        Object.values(mockStudents).forEach((student) => {
          const currentStatus =
            mockAttendance[selectedDate]?.[student.id] || "pending";

          const row = document.createElement("tr");
          row.className = "border-b border-white/10";
          row.innerHTML = `
                    <td class="p-3">${student.id}</td>
                    <td class="p-3">${student.name}</td>
                    <td class="p-3">${student.class}</td>
                    <td class="p-3">
                        <span class="px-3 py-1 rounded-full text-sm status-${currentStatus}">
                            ${
                              currentStatus.charAt(0).toUpperCase() +
                              currentStatus.slice(1)
                            }
                        </span>
                    </td>
                    <td class="p-3">
                        <select onchange="updateAttendanceStatus('${
                          student.id
                        }', this.value)"
                                class="p-2 rounded bg-white/20 text-white border border-white/30 focus:outline-none">
                            <option value="pending" ${
                              currentStatus === "pending" ? "selected" : ""
                            }>Pending</option>
                            <option value="present" ${
                              currentStatus === "present" ? "selected" : ""
                            }>Present</option>
                            <option value="absent" ${
                              currentStatus === "absent" ? "selected" : ""
                            }>Absent</option>
                            <option value="late" ${
                              currentStatus === "late" ? "selected" : ""
                            }>Late</option>
                            <option value="excused" ${
                              currentStatus === "excused" ? "selected" : ""
                            }>Excused</option>
                        </select>
                    </td>
                `;
          tbody.appendChild(row);
        });
      }

      function updateAttendanceStatus(studentId, status) {
        const selectedDate = document.getElementById("attendanceDate").value;

        if (!mockAttendance[selectedDate]) {
          mockAttendance[selectedDate] = {};
        }

        mockAttendance[selectedDate][studentId] = status;
        updateAdminStats();
        loadStudentAttendanceTable();
      }

      function saveAttendance() {
        showMessage(
          "Success",
          "Attendance records have been saved successfully!"
        );
        updateAdminStats();
      }

      // Parent Dashboard Functions
      function showParentDashboard() {
        document.getElementById("parentDashboard").classList.remove("hidden");
        loadParentAttendanceView();
      }

      function loadParentAttendanceView() {
        const parentView = document.getElementById("parentAttendanceView");
        const children = currentUser.children || [];

        if (children.length === 0) {
          parentView.innerHTML = `
                <div class="text-center text-gray-300 py-8">
                    <i class="fas fa-info-circle text-4xl mb-4"></i>
                    <p>No children found in the system.</p>
                </div>
            `;
          return;
        }

        let html = "";
        children.forEach((childId) => {
          const student = mockStudents[childId];
          if (student) {
            html += `
                    <div class="crystal-screen p-4 mb-4">
                        <h4 class="text-lg font-bold text-white mb-3 flex items-center">
                            <i class="fas fa-user-graduate mr-2"></i>${student.name} (${student.class})
                        </h4>
                        <div class="overflow-x-auto">
                            <table class="w-full text-white text-sm">
                                <thead>
                                    <tr class="border-b border-white/20">
                                        <th class="text-left p-2">Date</th>
                                        <th class="text-left p-2">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                `;

            // Get last 7 days of attendance
            for (let i = 6; i >= 0; i--) {
              const date = new Date(Date.now() - i * 86400000)
                .toISOString()
                .split("T")[0];
              const status = mockAttendance[date]?.[childId] || "pending";
              const formattedDate = new Date(date).toLocaleDateString();

              html += `
                        <tr class="border-b border-white/10">
                            <td class="p-2">${formattedDate}</td>
                            <td class="p-2">
                                <span class="px-2 py-1 rounded-full text-xs status-${status}">
                                    ${
                                      status.charAt(0).toUpperCase() +
                                      status.slice(1)
                                    }
                                </span>
                            </td>
                        </tr>
                    `;
            }

            html += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                `;
          }
        });

        parentView.innerHTML = html;
      }

      // Student Dashboard Functions
      function showStudentDashboard() {
        document.getElementById("studentDashboard").classList.remove("hidden");
        loadStudentAttendanceView();
      }

      function loadStudentAttendanceView() {
        const studentView = document.getElementById("studentAttendanceView");
        const studentId = currentUser.studentId;
        const student = mockStudents[studentId];

        if (!student) {
          studentView.innerHTML = `
                <div class="text-center text-gray-300 py-8">
                    <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                    <p>Student record not found.</p>
                </div>
            `;
          return;
        }

        let html = `
            <div class="crystal-screen p-4 mb-4">
                <h4 class="text-lg font-bold text-white mb-3 flex items-center">
                    <i class="fas fa-id-card mr-2"></i>Student Information
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-white">
                    <div>
                        <p class="text-gray-300">Student ID</p>
                        <p class="font-medium">${student.id}</p>
                    </div>
                    <div>
                        <p class="text-gray-300">Name</p>
                        <p class="font-medium">${student.name}</p>
                    </div>
                    <div>
                        <p class="text-gray-300">Class</p>
                        <p class="font-medium">${student.class}</p>
                    </div>
                </div>
            </div>

            <div class="crystal-screen p-4">
                <h4 class="text-lg font-bold text-white mb-3 flex items-center">
                    <i class="fas fa-calendar-check mr-2"></i>Attendance History
                </h4>
                <div class="overflow-x-auto">
                    <table class="w-full text-white">
                        <thead>
                            <tr class="border-b border-white/20">
                                <th class="text-left p-3">Date</th>
                                <th class="text-left p-3">Status</th>
                                <th class="text-left p-3">Notes</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        // Get last 14 days of attendance
        for (let i = 13; i >= 0; i--) {
          const date = new Date(Date.now() - i * 86400000)
            .toISOString()
            .split("T")[0];
          const status = mockAttendance[date]?.[studentId] || "pending";
          const formattedDate = new Date(date).toLocaleDateString();

          let notes = "";
          if (status === "late") notes = "Arrived 15 minutes late";
          else if (status === "excused") notes = "Medical appointment";
          else if (status === "absent") notes = "Unexcused absence";

          html += `
                <tr class="border-b border-white/10">
                    <td class="p-3">${formattedDate}</td>
                    <td class="p-3">
                        <span class="px-3 py-1 rounded-full text-sm status-${status}">
                            ${status.charAt(0).toUpperCase() + status.slice(1)}
                        </span>
                    </td>
                    <td class="p-3 text-gray-300">${notes}</td>
                </tr>
            `;
        }

        html += `
                        </tbody>
                    </table>
                </div>
            </div>
        `;

        studentView.innerHTML = html;
      }

      // Event Listeners and Initialization
      document.addEventListener("DOMContentLoaded", function () {
        initializeAttendance();
        updateTime();
        setInterval(updateTime, 1000);

        // Add event listener for date change in admin dashboard
        document
          .getElementById("attendanceDate")
          .addEventListener("change", function () {
            updateAdminStats();
            loadStudentAttendanceTable();
          });

        // Add Enter key support for login
        document.addEventListener("keypress", function (e) {
          if (
            e.key === "Enter" &&
            !document.getElementById("loginScreen").classList.contains("hidden")
          ) {
            login();
          }
        });
      });
    </script>
  </body>
</html>
